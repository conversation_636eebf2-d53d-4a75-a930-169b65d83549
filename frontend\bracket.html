<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALORANT MATCHMAKER - Tournament Bracket</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for bracket visualization */
        .bracket-container {
            display: flex;
            gap: 3rem;
            overflow-x: auto;
            padding: 2rem 0;
            min-height: 70vh;
        }

        .bracket-round {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            min-width: 250px;
        }

        .bracket-round h3 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--valorant-red);
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .bracket-match {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .bracket-match::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--valorant-cyan), var(--valorant-red));
            border-radius: 12px 12px 0 0;
        }

        .bracket-match:hover {
            transform: translateY(-3px);
            border-color: var(--valorant-cyan);
            box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
        }

        .match-teams {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .match-team {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: var(--dark-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .match-team.winner {
            border-color: var(--success-green);
            background: rgba(0, 245, 160, 0.1);
            color: var(--success-green);
            font-weight: 600;
        }

        .match-team.loser {
            opacity: 0.6;
            color: var(--text-secondary);
        }

        .team-name {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .match-vs {
            text-align: center;
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin: 0.5rem 0;
        }

        .match-winner {
            text-align: center;
            margin-top: 1rem;
            padding: 0.5rem;
            background: rgba(0, 245, 160, 0.1);
            border-radius: 6px;
            color: var(--success-green);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .match-tbd {
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
            padding: 1rem;
        }

        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
        }

        .bracket-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .bracket-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 3px;
            background: linear-gradient(45deg, var(--valorant-red), var(--valorant-cyan));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .bracket-title .subtitle {
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 500;
        }

        /* Responsive design for bracket */
        @media (max-width: 768px) {
            .bracket-container {
                flex-direction: column;
                gap: 2rem;
            }

            .bracket-round {
                min-width: auto;
            }

            .back-button {
                position: static;
                margin-bottom: 2rem;
            }
        }

        /* Connection lines (optional enhancement) */
        .bracket-round:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -1.5rem;
            top: 50%;
            width: 3rem;
            height: 2px;
            background: linear-gradient(90deg, var(--valorant-cyan), var(--valorant-red));
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="index.html" class="btn btn-secondary back-button">← Back to Teams</a>

    <div class="container">
        <!-- Title -->
        <div class="bracket-title">
            <h1>Tournament Bracket</h1>
            <p class="subtitle">Single Elimination</p>
        </div>

        <!-- Bracket Display -->
        <div id="bracketDisplay" class="bracket-container">
            <!-- Bracket will be rendered here -->
        </div>

        <!-- No Bracket Message -->
        <div id="noBracket" class="card" style="text-align: center; display: none;">
            <h2>No Bracket Available</h2>
            <p style="color: var(--text-secondary); margin-bottom: 2rem;">
                No tournament bracket has been generated yet.
            </p>
            <a href="index.html" class="btn btn-primary">Go Back to Add Teams</a>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8001';

        // Load and display bracket on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBracket();
        });

        // Load bracket from API
        async function loadBracket() {
            try {
                const response = await fetch(`${API_BASE_URL}/bracket`);
                if (response.ok) {
                    const data = await response.json();
                    const bracket = data.bracket || {};
                    displayBracket(bracket);
                } else {
                    showNoBracket();
                }
            } catch (error) {
                console.error('Error loading bracket:', error);
                showNoBracket();
            }
        }

        // Display bracket in tree format
        function displayBracket(bracket) {
            const bracketDisplay = document.getElementById('bracketDisplay');
            const noBracket = document.getElementById('noBracket');

            if (!bracket.rounds || bracket.rounds.length === 0) {
                showNoBracket();
                return;
            }

            noBracket.style.display = 'none';
            bracketDisplay.style.display = 'flex';

            // Generate rounds HTML
            const roundsHtml = bracket.rounds.map(round => {
                const matchesHtml = round.matches.map((match, index) => {
                    const team1Winner = match.winner === match.team1;
                    const team2Winner = match.winner === match.team2;
                    const hasWinner = match.winner && match.winner !== 'TBD';

                    const canComplete = match.team1 && match.team2 &&
                                       !match.team1.startsWith('Winner of') &&
                                       !match.team2.startsWith('Winner of') &&
                                       match.team1 !== 'BYE' && match.team2 !== 'BYE' &&
                                       !match.winner;

                    return `
                        <div class="bracket-match">
                            <div class="match-teams">
                                <div class="match-team ${team1Winner ? 'winner' : (hasWinner ? 'loser' : '')}">
                                    <span class="team-name">${match.team1 || 'TBD'}</span>
                                </div>
                                <div class="match-vs">VS</div>
                                <div class="match-team ${team2Winner ? 'winner' : (hasWinner ? 'loser' : '')}">
                                    <span class="team-name">${match.team2 || 'TBD'}</span>
                                </div>
                            </div>
                            ${hasWinner ? `
                                <div class="match-winner">
                                    🏆 ${match.winner}
                                </div>
                            ` : canComplete ? `
                                <div style="display: flex; gap: 0.5rem; margin-top: 1rem; justify-content: center;">
                                    <button class="btn btn-primary" onclick="completeMatch(${round.round}, ${index}, '${match.team1}')" style="padding: 0.5rem 1rem; font-size: 0.8rem;">
                                        ${match.team1} Wins
                                    </button>
                                    <button class="btn btn-primary" onclick="completeMatch(${round.round}, ${index}, '${match.team2}')" style="padding: 0.5rem 1rem; font-size: 0.8rem;">
                                        ${match.team2} Wins
                                    </button>
                                </div>
                            ` : `
                                <div class="match-tbd">
                                    ${match.team1 && match.team2 ? 'Match Pending' : 'Waiting for previous matches'}
                                </div>
                            `}
                        </div>
                    `;
                }).join('');

                return `
                    <div class="bracket-round">
                        <h3>${round.roundName || `Round ${round.round}`}</h3>
                        ${matchesHtml}
                    </div>
                `;
            }).join('');

            bracketDisplay.innerHTML = roundsHtml;
        }

        // Show no bracket message
        function showNoBracket() {
            const bracketDisplay = document.getElementById('bracketDisplay');
            const noBracket = document.getElementById('noBracket');

            bracketDisplay.style.display = 'none';
            noBracket.style.display = 'block';
        }

        // Complete a match with a winner
        async function completeMatch(roundNum, matchIndex, winner) {
            if (!confirm(`Confirm that ${winner} won this match?`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/bracket/match/complete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        round_num: roundNum,
                        match_index: matchIndex,
                        winner: winner
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`Match completed! ${winner} advances to the next round.`);
                    displayBracket(result.bracket);
                } else {
                    const error = await response.json();
                    alert(`Error: ${error.detail}`);
                }
            } catch (error) {
                console.error('Error completing match:', error);
                alert('Network error. Please check if the backend server is running.');
            }
        }
    </script>
</body>
</html>
