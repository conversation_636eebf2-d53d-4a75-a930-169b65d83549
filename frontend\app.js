// Configuration
const API_BASE_URL = 'http://localhost:8001';

// Global state
let teams = [];
let bracket = {};
let valorantRanks = [
    { name: 'Iron 1', filename: 'Iron_1_Rank.png' },
    { name: 'Iron 2', filename: 'Iron_2_Rank.png' },
    { name: 'Iron 3', filename: 'Iron_3_Rank.png' },
    { name: 'Bronze 1', filename: 'Bronze_1_Rank.png' },
    { name: 'Bronze 2', filename: 'Bronze_2_Rank.png' },
    { name: 'Bronze 3', filename: 'Bronze_3_Rank.png' },
    { name: 'Silver 1', filename: 'Silver_1_Rank.png' },
    { name: 'Silver 2', filename: 'Silver_2_Rank.png' },
    { name: 'Silver 3', filename: 'Silver_3_Rank.png' },
    { name: 'Gold 1', filename: 'Gold_1_Rank.png' },
    { name: 'Gold 2', filename: 'Gold_2_Rank.png' },
    { name: 'Gold 3', filename: 'Gold_3_Rank.png' },
    { name: 'Platinum 1', filename: 'Platinum_1_Rank.png' },
    { name: 'Platinum 2', filename: 'Platinum_2_Rank.png' },
    { name: 'Platinum 3', filename: 'Platinum_3_Rank.png' },
    { name: 'Diamond 1', filename: 'Diamond_1_Rank.png' },
    { name: 'Diamond 2', filename: 'Diamond_2_Rank.png' },
    { name: 'Diamond 3', filename: 'Diamond_3_Rank.png' },
    { name: 'Ascendant 1', filename: 'Ascendant_1_Rank.png' },
    { name: 'Ascendant 2', filename: 'Ascendant_2_Rank.png' },
    { name: 'Ascendant 3', filename: 'Ascendant_3_Rank.png' },
    { name: 'Immortal 1', filename: 'Immortal_1_Rank.png' },
    { name: 'Immortal 2', filename: 'Immortal_2_Rank.png' },
    { name: 'Immortal 3', filename: 'Immortal_3_Rank.png' },
    { name: 'Radiant', filename: 'Radiant_Rank.png' }
];

// DOM elements
const teamForm = document.getElementById('teamForm');
const playersContainer = document.getElementById('playersContainer');
const teamsList = document.getElementById('teamsList');
const generateBracketBtn = document.getElementById('generateBracketBtn');
const clearBracketBtn = document.getElementById('clearBracketBtn');
const bracketContainer = document.getElementById('bracketContainer');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    initializePlayerRows();
    loadTeams();
    loadBracket();
});

// Setup event listeners
function setupEventListeners() {
    // Tab switching
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', () => switchTab(tab.dataset.tab));
    });

    // Team form submission
    teamForm.addEventListener('submit', handleTeamSubmit);

    // Generate bracket button
    generateBracketBtn.addEventListener('click', generateBracket);

    // Clear bracket button
    clearBracketBtn.addEventListener('click', clearBracket);
}

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');

    // Load data for the active tab
    if (tabName === 'bracket') {
        loadBracket();
    }
}

// Initialize player rows
function initializePlayerRows() {
    // Add 5 default player rows
    for (let i = 0; i < 5; i++) {
        addPlayerRow();
    }
}

// Reset team form to default state
function resetTeamForm() {
    // Reset form inputs
    teamForm.reset();

    // Clear all existing player rows
    playersContainer.innerHTML = '';

    // Add exactly 5 new empty player rows
    for (let i = 0; i < 5; i++) {
        addPlayerRow();
    }

    console.log(`Form reset complete. Player rows count: ${playersContainer.querySelectorAll('.player-row').length}`);
}

// Add player row
function addPlayerRow() {
    const playerRow = document.createElement('div');
    playerRow.className = 'player-row';
    
    const rankOptions = valorantRanks.map(rank => 
        `<option value="${rank.filename}">${rank.name}</option>`
    ).join('');

    playerRow.innerHTML = `
        <input type="text" class="player-name" placeholder="Player Name" required>
        <select class="player-rank">
            <option value="">Select Rank</option>
            ${rankOptions}
        </select>
        <button type="button" class="btn btn-danger" onclick="removePlayerRow(this)">Remove</button>
    `;

    playersContainer.appendChild(playerRow);
}

// Remove player row
function removePlayerRow(button) {
    const playerRows = playersContainer.querySelectorAll('.player-row');
    if (playerRows.length > 1) {
        button.closest('.player-row').remove();
    } else {
        alert('At least one player is required!');
    }
}

// Handle team form submission
async function handleTeamSubmit(e) {
    e.preventDefault();

    const teamName = document.getElementById('teamName').value.trim();
    const teamIcon = document.getElementById('teamIcon').value.trim();

    // Collect player data
    const playerRows = playersContainer.querySelectorAll('.player-row');
    const players = [];

    playerRows.forEach(row => {
        const name = row.querySelector('.player-name').value.trim();
        const rank = row.querySelector('.player-rank').value;

        if (name) {
            players.push({
                name: name,
                rank: rank || null
            });
        }
    });

    if (players.length === 0) {
        alert('Please add at least one player!');
        return;
    }

    // Prepare team data
    const teamData = {
        name: teamName,
        players: players,
        icon: teamIcon || null
    };

    try {
        const response = await fetch(`${API_BASE_URL}/teams/add`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(teamData)
        });

        if (response.ok) {
            const result = await response.json();
            alert('Team added successfully!');
            resetTeamForm();
            loadTeams();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error adding team:', error);
        alert('Network error. Please check if the backend server is running.');
    }
}

// Load teams
async function loadTeams() {
    try {
        const response = await fetch(`${API_BASE_URL}/teams`);
        if (response.ok) {
            const data = await response.json();
            teams = data.teams || [];
            displayTeams();
        }
    } catch (error) {
        console.error('Error loading teams:', error);
        teamsList.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">Error loading teams. Please check if the backend server is running.</p>';
    }
}

// Display teams
function displayTeams() {
    if (teams.length === 0) {
        teamsList.innerHTML = '<p style="text-align: center; color: var(--text-secondary); font-style: italic;">No teams added yet. Add your first team above!</p>';
        return;
    }

    teamsList.innerHTML = teams.map(team => {
        const playersHtml = team.players.map(player => {
            const rankImg = player.rank ? 
                `<img src="assets/ranks/${player.rank}" alt="${getRankDisplayName(player.rank)}" class="rank-icon">` : '';
            return `
                <div class="team-player">
                    ${rankImg}
                    <span>${player.name}</span>
                </div>
            `;
        }).join('');

        return `
            <div class="team-card">
                <h3>${team.name}</h3>
                ${team.icon ? `<img src="${team.icon}" alt="Team Icon" style="width: 32px; height: 32px; border-radius: 4px; margin-bottom: 1rem;">` : ''}
                <div class="team-players">
                    ${playersHtml}
                </div>
                <p style="color: var(--valorant-cyan); font-weight: 600;">Seed Score: ${team.seedScore || 0}</p>
                <div class="team-actions">
                    <button class="btn btn-danger" onclick="deleteTeam('${team.name}')">Delete</button>
                </div>
            </div>
        `;
    }).join('');
}

// Get rank display name
function getRankDisplayName(filename) {
    const rank = valorantRanks.find(r => r.filename === filename);
    return rank ? rank.name : 'Unknown Rank';
}

// Delete team
async function deleteTeam(teamName) {
    if (!confirm(`Are you sure you want to delete team "${teamName}"?`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/teams/${encodeURIComponent(teamName)}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            alert('Team deleted successfully!');
            loadTeams();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error deleting team:', error);
        alert('Network error. Please check if the backend server is running.');
    }
}

// Generate bracket
async function generateBracket() {
    if (teams.length < 2) {
        alert('At least 2 teams are required to generate a bracket!');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/bracket/generate`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            alert('Bracket generated successfully!');
            loadBracket();
            switchTab('bracket');
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error generating bracket:', error);
        alert('Network error. Please check if the backend server is running.');
    }
}

// Load bracket
async function loadBracket() {
    try {
        const response = await fetch(`${API_BASE_URL}/bracket`);
        if (response.ok) {
            const data = await response.json();
            bracket = data.bracket || {};
            displayBracket();
        }
    } catch (error) {
        console.error('Error loading bracket:', error);
        bracketContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">Error loading bracket. Please check if the backend server is running.</p>';
    }
}

// Display bracket - Optimized for compact layout with skill differences
function displayBracket() {
    if (!bracket.rounds || bracket.rounds.length === 0) {
        bracketContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary); font-style: italic;">No bracket generated yet. Add teams and click "Generate Tournament Bracket" to create one.</p>';
        return;
    }

    const roundsHtml = bracket.rounds.map(round => {
        const matchesHtml = round.matches.map((match, index) => {
            const canComplete = match.team1 && match.team2 &&
                               !match.team1.startsWith('Winner of') &&
                               !match.team2.startsWith('Winner of') &&
                               match.team1 !== 'BYE' && match.team2 !== 'BYE' &&
                               !match.winner;

            // Get skill difference class for styling
            const skillDiff = match.skillDiff || 0;
            let skillDiffClass = 'skill-diff';
            if (skillDiff <= 0.2) skillDiffClass += ' low';
            else if (skillDiff <= 0.5) skillDiffClass += ' medium';
            else skillDiffClass += ' high';

            return `
                <div class="bracket-match-preview">
                    <div class="match-teams-preview">
                        <span style="color: ${match.winner === match.team1 ? 'var(--success-green)' : 'var(--text-primary)'}">${match.team1 || 'TBD'}</span>
                        <span class="vs-text">VS</span>
                        <span style="color: ${match.winner === match.team2 ? 'var(--success-green)' : 'var(--text-primary)'}">${match.team2 || 'TBD'}</span>
                        ${match.skillDiff !== undefined ? `<span class="${skillDiffClass}">Diff: ${match.skillDiff}</span>` : ''}
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        ${match.winner ? `
                            <span style="color: var(--success-green); font-weight: 600; font-size: 0.8rem;">🏆 ${match.winner}</span>
                        ` : canComplete ? `
                            <button class="btn btn-primary" onclick="completeMatch(${round.round}, ${index}, '${match.team1}')" style="padding: 0.3rem 0.6rem; font-size: 0.7rem; margin-right: 0.3rem;">
                                ${match.team1}
                            </button>
                            <button class="btn btn-primary" onclick="completeMatch(${round.round}, ${index}, '${match.team2}')" style="padding: 0.3rem 0.6rem; font-size: 0.7rem;">
                                ${match.team2}
                            </button>
                        ` : `
                            <span style="color: var(--text-secondary); font-style: italic; font-size: 0.8rem;">
                                ${match.team1 && match.team2 ? 'Pending' : 'TBD'}
                            </span>
                        `}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="round-section" style="margin-bottom: 1.2rem;">
                <h3 style="color: var(--valorant-red); margin-bottom: 0.7rem; text-transform: uppercase; letter-spacing: 1px; font-size: 1.1rem;">
                    ${round.roundName || `Round ${round.round}`}
                </h3>
                <div class="bracket-preview">
                    ${matchesHtml}
                </div>
            </div>
        `;
    }).join('');

    bracketContainer.innerHTML = roundsHtml;
}

// Clear bracket
async function clearBracket() {
    if (!confirm('Are you sure you want to clear the current bracket?')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/bracket`, {
            method: 'DELETE'
        });

        if (response.ok) {
            alert('Bracket cleared successfully!');
            loadBracket();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error clearing bracket:', error);
        alert('Network error. Please check if the backend server is running.');
    }
}

// Complete a match with a winner
async function completeMatch(roundNum, matchIndex, winner) {
    if (!confirm(`Confirm that ${winner} won this match?`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/bracket/match/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                round_num: roundNum,
                match_index: matchIndex,
                winner: winner
            })
        });

        if (response.ok) {
            const result = await response.json();
            alert(`Match completed! ${winner} advances to the next round.`);
            bracket = result.bracket;
            displayBracket();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        console.error('Error completing match:', error);
        alert('Network error. Please check if the backend server is running.');
    }
}
