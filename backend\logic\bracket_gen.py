"""
Tournament bracket generation logic
"""
import math
import random
from typing import List, Dict, <PERSON>, <PERSON><PERSON>

def generate_single_elimination_bracket(teams: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate a single elimination bracket with proper seeding
    """
    if not teams:
        return {"rounds": []}
    
    # Sort teams by seed score (highest first)
    sorted_teams = sorted(teams, key=lambda x: x.get('seedScore', 0), reverse=True)
    
    # Calculate number of rounds needed
    num_teams = len(sorted_teams)
    num_rounds = math.ceil(math.log2(num_teams)) if num_teams > 1 else 1
    bracket_size = 2 ** num_rounds
    
    # Create first round with seeded matchups
    first_round_matches = []
    
    if num_teams == 1:
        # Special case: only one team
        return {
            "rounds": [{
                "round": 1,
                "matches": [{
                    "team1": sorted_teams[0]["name"],
                    "team2": "BYE",
                    "winner": sorted_teams[0]["name"]
                }]
            }]
        }
    
    # Add BYEs if needed
    teams_with_byes = sorted_teams.copy()
    byes_needed = bracket_size - num_teams
    
    for i in range(byes_needed):
        teams_with_byes.append({"name": "BYE", "seedScore": -1})
    
    # Create seeded matchups (1 vs last, 2 vs second-to-last, etc.)
    for i in range(bracket_size // 2):
        team1 = teams_with_byes[i]
        team2 = teams_with_byes[bracket_size - 1 - i]
        
        match = {
            "team1": team1["name"],
            "team2": team2["name"],
            "winner": None
        }
        
        # Auto-advance if opponent is BYE
        if team2["name"] == "BYE":
            match["winner"] = team1["name"]
        elif team1["name"] == "BYE":
            match["winner"] = team2["name"]
        
        first_round_matches.append(match)
    
    rounds = [{
        "round": 1,
        "matches": first_round_matches
    }]
    
    # Generate subsequent rounds
    for round_num in range(2, num_rounds + 1):
        prev_round = rounds[-1]
        current_matches = []

        # Pair up winners from previous round
        for i in range(0, len(prev_round["matches"]), 2):
            if i + 1 < len(prev_round["matches"]):
                match1 = prev_round["matches"][i]
                match2 = prev_round["matches"][i + 1]

                # Use actual winner if available, otherwise use reference
                team1 = match1.get("winner") if match1.get("winner") else f"Winner of R{round_num-1}M{i+1}"
                team2 = match2.get("winner") if match2.get("winner") else f"Winner of R{round_num-1}M{i+2}"

                match_data = {
                    "team1": team1,
                    "team2": team2,
                    "winner": None,
                    "sourceMatch1": {"round": round_num-1, "match": i},
                    "sourceMatch2": {"round": round_num-1, "match": i+1}
                }

                # Auto-advance if one team is already determined and other is BYE
                if team1 != f"Winner of R{round_num-1}M{i+1}" and team2 == "BYE":
                    match_data["winner"] = team1
                elif team2 != f"Winner of R{round_num-1}M{i+2}" and team1 == "BYE":
                    match_data["winner"] = team2

                current_matches.append(match_data)
            else:
                # Odd number of matches, last team gets bye
                match1 = prev_round["matches"][i]
                team1 = match1.get("winner") if match1.get("winner") else f"Winner of R{round_num-1}M{i+1}"

                current_matches.append({
                    "team1": team1,
                    "team2": "BYE",
                    "winner": team1 if team1 != f"Winner of R{round_num-1}M{i+1}" else None,
                    "sourceMatch1": {"round": round_num-1, "match": i},
                    "sourceMatch2": None
                })

        rounds.append({
            "round": round_num,
            "matches": current_matches
        })
    
    return {"rounds": rounds}

def update_bracket_with_winner(bracket: Dict[str, Any], round_num: int, match_index: int, winner: str) -> Dict[str, Any]:
    """
    Update bracket when a match is completed with a winner
    """
    if not bracket.get("rounds") or round_num < 1 or round_num > len(bracket["rounds"]):
        return bracket

    # Update the match with the winner
    round_data = bracket["rounds"][round_num - 1]  # Convert to 0-based index
    if match_index < 0 or match_index >= len(round_data["matches"]):
        return bracket

    round_data["matches"][match_index]["winner"] = winner

    # Update subsequent rounds that depend on this match
    for next_round_idx in range(round_num, len(bracket["rounds"])):
        next_round = bracket["rounds"][next_round_idx]

        for next_match_idx, next_match in enumerate(next_round["matches"]):
            # Check if this match depends on the completed match
            source1 = next_match.get("sourceMatch1")
            source2 = next_match.get("sourceMatch2")

            updated = False

            if source1 and source1["round"] == round_num and source1["match"] == match_index:
                next_match["team1"] = winner
                updated = True

            if source2 and source2["round"] == round_num and source2["match"] == match_index:
                next_match["team2"] = winner
                updated = True

            # If both teams are now known and one is BYE, auto-advance
            if updated and next_match["team1"] and next_match["team2"]:
                if next_match["team2"] == "BYE":
                    next_match["winner"] = next_match["team1"]
                elif next_match["team1"] == "BYE":
                    next_match["winner"] = next_match["team2"]

    return bracket

def get_round_name(round_num: int, total_rounds: int) -> str:
    """Get descriptive name for tournament round"""
    if total_rounds == 1:
        return "Final"
    elif round_num == total_rounds:
        return "Grand Final"
    elif round_num == total_rounds - 1:
        return "Semi-Final"
    elif round_num == total_rounds - 2:
        return "Quarter-Final"
    elif round_num == 1:
        return "First Round"
    else:
        return f"Round {round_num}"


def generate_closest_match_bracket(teams: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Generate matchups using Closest Match Skill system.
    Pairs teams with the smallest possible skill gaps.

    Args:
        teams: List of teams with 'name' and 'rank_score' fields

    Returns:
        List of matchups with team1, team2, and diff fields
    """
    if not teams:
        return []

    if len(teams) % 2 != 0:
        raise ValueError("Number of teams must be even for closest match pairing")

    # Sort teams by rank_score (ascending order)
    sorted_teams = sorted(teams, key=lambda x: x.get('rank_score', 0))

    matchups = []
    used_teams = set()

    # Greedy approach: pair each team with its closest available opponent
    for i, team in enumerate(sorted_teams):
        if team['name'] in used_teams:
            continue

        best_opponent = None
        min_diff = float('inf')

        # Find the closest opponent that hasn't been used
        for j, opponent in enumerate(sorted_teams):
            if i != j and opponent['name'] not in used_teams:
                diff = abs(team['rank_score'] - opponent['rank_score'])
                if diff < min_diff:
                    min_diff = diff
                    best_opponent = opponent

        if best_opponent:
            # Create matchup
            matchup = {
                "team1": team['name'],
                "team2": best_opponent['name'],
                "diff": round(min_diff, 1)
            }
            matchups.append(matchup)

            # Mark both teams as used
            used_teams.add(team['name'])
            used_teams.add(best_opponent['name'])

    return matchups
