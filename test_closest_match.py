#!/usr/bin/env python3
"""
Test script for the Closest Match Skill system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from logic.bracket_gen import generate_closest_match_bracket

# Test data from the user's example
test_teams = [
    {"name": "Phantom Reign", "rank_score": 4.8},
    {"name": "Spike Hunters", "rank_score": 4.6},
    {"name": "Ghost Clan", "rank_score": 4.2},
    {"name": "BlitzCore", "rank_score": 4.1},
    {"name": "Fade Squad", "rank_score": 3.8},
    {"name": "Shadow Agents", "rank_score": 3.7},
    {"name": "Ultima Five", "rank_score": 3.5},
    {"name": "HexStorm", "rank_score": 3.2}
]

def test_closest_match():
    """Test the closest match algorithm"""
    print("🎯 Testing Closest Match Skill System")
    print("=" * 50)
    
    print("\n📊 Input Teams (sorted by rank_score):")
    sorted_teams = sorted(test_teams, key=lambda x: x['rank_score'])
    for team in sorted_teams:
        print(f"  {team['name']}: {team['rank_score']}")
    
    print("\n🔄 Generating matchups...")
    try:
        matchups = generate_closest_match_bracket(test_teams)
        
        print(f"\n✅ Generated {len(matchups)} matchups:")
        print("-" * 40)
        
        total_diff = 0
        for i, matchup in enumerate(matchups, 1):
            print(f"{i}. {matchup['team1']} vs {matchup['team2']} (diff: {matchup['diff']})")
            total_diff += matchup['diff']
        
        print(f"\n📈 Total skill difference: {total_diff}")
        print(f"📊 Average skill difference: {total_diff / len(matchups):.2f}")
        
        # Verify expected output
        expected_matchups = [
            {"team1": "Phantom Reign", "team2": "Spike Hunters", "diff": 0.2},
            {"team1": "Ghost Clan", "team2": "BlitzCore", "diff": 0.1},
            {"team1": "Fade Squad", "team2": "Shadow Agents", "diff": 0.1},
            {"team1": "Ultima Five", "team2": "HexStorm", "diff": 0.3}
        ]
        
        print("\n🎯 Expected vs Actual:")
        print("-" * 40)
        for expected in expected_matchups:
            found = False
            for actual in matchups:
                if ((actual['team1'] == expected['team1'] and actual['team2'] == expected['team2']) or
                    (actual['team1'] == expected['team2'] and actual['team2'] == expected['team1'])):
                    print(f"✅ {expected['team1']} vs {expected['team2']} - Expected diff: {expected['diff']}, Actual diff: {actual['diff']}")
                    found = True
                    break
            if not found:
                print(f"❌ Missing: {expected['team1']} vs {expected['team2']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_closest_match()
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
        sys.exit(1)
