<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALORANT MATCHMAKER</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <h1>VALORANT MATCHMAKER</h1>
        <p class="subtitle">Tournament Bracket Generator</p>
    </header>

    <!-- Navigation -->
    <div class="container">
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="teams">TEAMS</button>
            <button class="nav-tab" data-tab="bracket">BRACKET</button>
        </nav>

        <!-- Teams Tab -->
        <div id="teams" class="tab-content active">
            <!-- Add Team Form -->
            <div class="card">
                <h2>Add Team</h2>
                <form id="teamForm">
                    <div class="form-group">
                        <label for="teamName">Team Name</label>
                        <input type="text" id="teamName" required placeholder="Enter team name">
                    </div>

                    <div class="form-group">
                        <label for="teamIcon">Team Icon URL (Optional)</label>
                        <input type="url" id="teamIcon" placeholder="https://example.com/icon.png">
                    </div>

                    <div class="form-group">
                        <label>Players (5 required)</label>
                        <div id="playersContainer">
                            <!-- Player rows will be added here -->
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="addPlayerRow()">Add Player</button>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Add Team</button>
                    </div>
                </form>
            </div>

            <!-- Teams List -->
            <div class="card">
                <h2>Teams</h2>
                <div id="teamsList" class="teams-list">
                    <!-- Teams will be displayed here -->
                </div>
                <div style="text-align: center; margin-top: 1rem;">
                    <button id="generateBracketBtn" class="btn btn-primary" style="font-size: 0.9rem; padding: 0.7rem 1.5rem;">
                        Generate Tournament Bracket
                    </button>
                </div>
            </div>
        </div>

        <!-- Bracket Tab -->
        <div id="bracket" class="tab-content">
            <div class="card">
                <h2>Tournament Bracket</h2>
                <div id="bracketContainer">
                    <p style="text-align: center; color: var(--text-secondary); font-style: italic;">
                        No bracket generated yet. Add teams and click "Generate Tournament Bracket" to create one.
                    </p>
                </div>
                <div style="text-align: center; margin-top: 1rem;">
                    <button id="clearBracketBtn" class="btn btn-danger">Clear Bracket</button>
                    <a href="bracket.html" class="btn btn-secondary" style="text-decoration: none; display: inline-block; margin-left: 0.7rem;">
                        View Full Bracket
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
