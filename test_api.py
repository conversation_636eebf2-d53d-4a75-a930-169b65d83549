#!/usr/bin/env python3
"""
Test script for the API with Closest Match Skill system
"""

import requests
import json

API_BASE_URL = "http://localhost:8001"

def test_api():
    """Test the API endpoints"""
    print("🎯 Testing API with Closest Match Skill System")
    print("=" * 50)
    
    try:
        # Test getting teams
        print("\n📊 Getting current teams...")
        response = requests.get(f"{API_BASE_URL}/teams")
        if response.status_code == 200:
            teams_data = response.json()
            teams = teams_data.get('teams', [])
            print(f"✅ Found {len(teams)} teams")
            
            for team in teams:
                print(f"  - {team['name']}: seedScore={team.get('seedScore', 'N/A')}")
        else:
            print(f"❌ Failed to get teams: {response.status_code}")
            return False
        
        # Test generating bracket
        print("\n🔄 Generating bracket with Closest Match Skill system...")
        response = requests.post(f"{API_BASE_URL}/bracket/generate")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result['message']}")
            
            bracket = result.get('bracket', {})
            rounds = bracket.get('rounds', [])
            
            if rounds:
                first_round = rounds[0]
                matches = first_round.get('matches', [])
                
                print(f"\n📋 Generated {len(matches)} matches:")
                print("-" * 40)
                
                for i, match in enumerate(matches, 1):
                    team1 = match['team1']
                    team2 = match['team2']
                    skill_diff = match.get('skillDiff', 'N/A')
                    print(f"{i}. {team1} vs {team2} (skill diff: {skill_diff})")
                
                return True
            else:
                print("❌ No rounds found in bracket")
                return False
        else:
            error_data = response.json()
            print(f"❌ Failed to generate bracket: {response.status_code}")
            print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API server. Make sure the backend is running on port 8001.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_api()
    if success:
        print("\n🎉 API test completed successfully!")
    else:
        print("\n💥 API test failed!")
