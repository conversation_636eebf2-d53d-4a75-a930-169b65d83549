"""
FastAPI backend for Valorant Tournament Bracket Generator
"""
import json
import os
from typing import List, Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from logic.bracket_gen import generate_single_elimination_bracket, get_round_name, update_bracket_with_winner, generate_closest_match_bracket
from logic.rank_map import calculate_team_seed_score

app = FastAPI(title="Valorant Tournament Bracket Generator")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serve static files (frontend)
app.mount("/static", StaticFiles(directory="../frontend"), name="static")

# Data file paths
TEAMS_FILE = "data/teams.json"
BRACKETS_FILE = "data/brackets.json"

# Pydantic models
class Player(BaseModel):
    name: str
    rank: Optional[str] = None

class Team(BaseModel):
    name: str
    players: List[Player]
    icon: Optional[str] = None

class BracketRequest(BaseModel):
    teams: List[str]  # Team names to include in bracket

class MatchResult(BaseModel):
    round_num: int
    match_index: int
    winner: str

# Utility functions
def load_teams() -> List[Dict[str, Any]]:
    """Load teams from JSON file"""
    if not os.path.exists(TEAMS_FILE):
        return []
    
    try:
        with open(TEAMS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []

def save_teams(teams: List[Dict[str, Any]]) -> None:
    """Save teams to JSON file"""
    os.makedirs(os.path.dirname(TEAMS_FILE), exist_ok=True)
    with open(TEAMS_FILE, 'w', encoding='utf-8') as f:
        json.dump(teams, f, indent=2, ensure_ascii=False)

def load_bracket() -> Dict[str, Any]:
    """Load bracket from JSON file"""
    if not os.path.exists(BRACKETS_FILE):
        return {}
    
    try:
        with open(BRACKETS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def save_bracket(bracket: Dict[str, Any]) -> None:
    """Save bracket to JSON file"""
    os.makedirs(os.path.dirname(BRACKETS_FILE), exist_ok=True)
    with open(BRACKETS_FILE, 'w', encoding='utf-8') as f:
        json.dump(bracket, f, indent=2, ensure_ascii=False)

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Valorant Tournament Bracket Generator API"}

@app.get("/teams")
async def get_teams():
    """Get all teams"""
    teams = load_teams()
    return {"teams": teams}

@app.post("/teams/add")
async def add_team(team: Team):
    """Add a new team"""
    teams = load_teams()
    
    # Check if team name already exists
    if any(t["name"].lower() == team.name.lower() for t in teams):
        raise HTTPException(status_code=400, detail="Team name already exists")
    
    # Calculate seed score
    players_data = [{"name": p.name, "rank": p.rank} for p in team.players]
    seed_score = calculate_team_seed_score(players_data)
    
    new_team = {
        "name": team.name,
        "players": players_data,
        "icon": team.icon,
        "seedScore": seed_score
    }
    
    teams.append(new_team)
    save_teams(teams)
    
    return {"message": "Team added successfully", "team": new_team}

@app.delete("/teams/{team_name}")
async def delete_team(team_name: str):
    """Delete a team"""
    teams = load_teams()
    
    # Find and remove team
    teams = [t for t in teams if t["name"] != team_name]
    save_teams(teams)
    
    return {"message": f"Team {team_name} deleted successfully"}

@app.post("/bracket/generate")
async def generate_bracket():
    """Generate tournament bracket using Closest Match Skill system"""
    teams = load_teams()

    if len(teams) < 2:
        raise HTTPException(status_code=400, detail="At least 2 teams required for bracket generation")

    if len(teams) % 2 != 0:
        raise HTTPException(status_code=400, detail="Number of teams must be even for closest match pairing")

    # Convert seedScore to rank_score for closest match algorithm
    teams_with_rank_score = []
    for team in teams:
        team_copy = team.copy()
        # Use seedScore as rank_score if rank_score doesn't exist
        team_copy['rank_score'] = team.get('rank_score', team.get('seedScore', 0))
        teams_with_rank_score.append(team_copy)

    # Generate closest match bracket
    matchups = generate_closest_match_bracket(teams_with_rank_score)

    # Format as single round bracket for compatibility with frontend
    bracket = {
        "rounds": [{
            "round": 1,
            "roundName": "First Round",
            "matches": []
        }]
    }

    # Convert matchups to bracket format
    for matchup in matchups:
        match = {
            "team1": matchup["team1"],
            "team2": matchup["team2"],
            "winner": None,
            "skillDiff": matchup["diff"]  # Add skill difference for display
        }
        bracket["rounds"][0]["matches"].append(match)

    # Save bracket
    save_bracket(bracket)

    return {"message": "Bracket generated successfully using Closest Match Skill system", "bracket": bracket}

@app.get("/bracket")
async def get_bracket():
    """Get current bracket"""
    bracket = load_bracket()
    return {"bracket": bracket}

@app.delete("/bracket")
async def clear_bracket():
    """Clear current bracket"""
    save_bracket({})
    return {"message": "Bracket cleared successfully"}

@app.post("/bracket/match/complete")
async def complete_match(result: MatchResult):
    """Complete a match and update bracket progression"""
    bracket = load_bracket()

    if not bracket.get("rounds"):
        raise HTTPException(status_code=400, detail="No bracket found")

    # Validate round and match indices
    if result.round_num < 1 or result.round_num > len(bracket["rounds"]):
        raise HTTPException(status_code=400, detail="Invalid round number")

    round_data = bracket["rounds"][result.round_num - 1]
    if result.match_index < 0 or result.match_index >= len(round_data["matches"]):
        raise HTTPException(status_code=400, detail="Invalid match index")

    match_data = round_data["matches"][result.match_index]

    # Validate winner is one of the teams in the match
    if result.winner not in [match_data["team1"], match_data["team2"]]:
        raise HTTPException(status_code=400, detail="Winner must be one of the teams in the match")

    # Update bracket with winner
    updated_bracket = update_bracket_with_winner(bracket, result.round_num, result.match_index, result.winner)
    save_bracket(updated_bracket)

    return {"message": "Match completed successfully", "bracket": updated_bracket}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
